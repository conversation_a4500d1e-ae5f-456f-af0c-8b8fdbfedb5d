<template>
  <div class="softbar flex items-center h-full">
    <el-dropdown
      class="dropdown-no-border w-100px"
      @command="handleCodeSelect"
    >
      <el-button size="small" type="text">
        <span>{{ agentStatus }}</span>
        <span style="margin-left: 4px;">▼</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="item in codeList"
            :key="item.id"
            :command="Number(item.code)"
            :style="{ color: item.code === 0 ? '#409eff' : 'red' }"
          >
            {{ item.codeName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- <el-input
      class="w-400px"
      placeholder="请输入号码"
      clearable
      v-model="callNumber"
      @keydown.enter="makeCall"
    /> -->
    <el-input
      class="w-400px"
      v-model="callNumber"
      style="width: 240px"
      placeholder="请输入号码"
      clearable
    />

    <div class="ml-10px"></div>
    <el-button id="login_btn" @click="showLogin" v-if="buttonInfo.login_btn">登录</el-button>
    <el-button id="makeCall_btn" @click="makeCall" type="primary" v-if="buttonInfo.makeCall_btn">外呼</el-button>
    <el-button id="hangup_btn" @click="hangUp" type="danger" v-if="buttonInfo.hangup_btn">挂断</el-button>
    <el-button id="answer_btn" @click="answer" type="primary" v-if="buttonInfo.answer_btn">接听</el-button>
    <el-button id="hold_btn" @click="hold" type="primary" v-if="buttonInfo.hold_btn">保持</el-button>
    <el-button id="retrieve_btn" @click="retrieve" type="primary" v-if="buttonInfo.retrieve_btn">取回</el-button>
    <el-button id="consult_btn" @click="consultBTN" type="primary" v-if="buttonInfo.consult_btn">咨询</el-button>
    <el-button id="ssc_btn" @click="ssc" type="primary" v-if="buttonInfo.ssc_btn">会议</el-button>
    <el-button id="sst_btn" @click="sst" type="primary" v-if="buttonInfo.sst_btn">转移</el-button>
    <el-button id="alternate_btn" @click="alternate" type="primary" v-if="buttonInfo.alternate_btn">切换</el-button>
    <el-button id="transfer_btn" @click="transfer" type="primary" v-if="buttonInfo.transfer_btn">转移</el-button>
    <el-button id="reconnect_btn" @click="reconnect" type="primary" v-if="buttonInfo.reconnect_btn">重连</el-button>
    <el-button id="conference_btn" @click="conference" type="primary" v-if="buttonInfo.conference_btn">会议</el-button>
    <!-- <el-button id="sendDTMF_btn" @click="sendDTMF" v-if="buttonInfo.sendDTMF_btn">发送DTMF</el-button> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import callService from '@/services/call/CallService'
import { useEmitt } from '@/hooks/web/useEmitt'
import { useMessage } from '@/hooks/web/useMessage'
import { CallEvents, CCEvents, type CallEventPayload } from '@/services/call/types'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

interface CodeItem {
  id: number
  code: number
  codeName: string
}
const agentStatus = ref('未就绪')
const { wsCache } = useCache()
const callNumber = ref('')
const { emitter } = useEmitt()
const message = useMessage()
const codeList = ref<CodeItem[]>([])

// 按钮控制
const buttonInfo = ref({
  login_btn: true,
  sendDTMF_btn: false,
  makeCall_btn: false,
  hangup_btn: false,
  answer_btn: false,
  consult_btn: false,
  hold_btn: false,
  retrieve_btn: false,
  ssc_btn: false,
  sst_btn: false,
  alternate_btn: false,
  transfer_btn: false,
  reconnect_btn: false,
  conference_btn: false,
  validatePwd_btn: false
})

// 按钮方法定义
const showLogin = () => {
  console.log('登录')
  const userInfo = wsCache.get(CACHE_KEY.USER)
  callService.signIn(userInfo)
}

const makeCall = () => {
  if (!callNumber.value) {
    message.notifyWarning('请先输入号码')
    return
  }
  console.log('外呼:', callNumber.value)
  callService.makeCall(callNumber.value)
}

const hangUp = () => {
  console.log('挂断')
  callService.hangup()
}

const answer = () => {
  console.log('接听')
  callService.answer()
}

const hold = () => {
  console.log('保持')
  callService.hold()
}

const retrieve = () => {
  console.log('取回')
  callService.retrieve()
}

const consultBTN = () => {
  console.log('咨询')
  if (!callNumber.value) return
  callService.consultation(callNumber.value)
}

const ssc = () => {
  console.log('会议')
  callService.conference()
}

const sst = () => {
  console.log('转移')
  callService.transferCall()
}

const alternate = () => {
  console.log('切换')
  callService.alternate()
}

const transfer = () => {
  console.log('转移')
  if (!callNumber.value) return
  callService.singleStepTransfer(callNumber.value)
}

const reconnect = () => {
  console.log('重连')
  callService.reconnect()
}

const conference = () => {
  console.log('会议')
  callService.singleStepConference(callNumber.value)
}

const sendDTMF = () => {
  console.log('发送DTMF')
  if (!callNumber.value) return
  callService.sendDtmfTone(callNumber.value)
}

const handleCodeSelect = (code: number) => {
  console.log('选中的代码:', code)
  if(code == 0){
    agentStatus.value = '就绪'
    setAgentStatus(0,"Ready")
    message.notifySuccess('就绪成功')
  }else{
    const selectedItem = codeList.value.find((item: CodeItem) => item.code === code)
    if (selectedItem) {
      console.log('选中的项目:', selectedItem)
      agentStatus.value = selectedItem.codeName
      setAgentStatus(selectedItem.code,"NotReady")
      message.notifySuccess('状态切换成功')
    }
  }
  
}
const setAgentStatus = (reasonCode: any, agentStatus: any) => {
  let userInfo = wsCache.get(CACHE_KEY.USER)
  callService.setState(userInfo, reasonCode, agentStatus)
}

const onRaw = (s: CallEventPayload) => {
  // console.log('onRaw', s)
  if(s.code === 201){
    const extraItem = {
      code: 0,
      codeName: '就绪',
      codeType: 0,
      domain: 'yihucc'
    }
    codeList.value = [extraItem, ...JSON.parse(s.message)]
    console.log('codeList', codeList.value)
  }
  if(s.code >= 500){
    message.notifyError(s.message || s.errMsg)
  }
}

const onAgentLoggedOn = (data: CallEventPayload) => {
  console.log('onAgentLoggedOn', data)
  message.notifySuccess('坐席登录成功')
}

const onAgentLoggedOff = () => {}
const onOriginated = () => {}
const onDelivered = () => {}
const onEstablished = () => {}
const onCleared = () => {}
const onHeld = () => {}
const onRetrieved = () => {}
const onTransfered = () => {}
const onError = () => {}

// 监听按钮状态变化
const onButtonStateChanged = (newButtonInfo: any) => {
  console.log('按钮状态更新:', newButtonInfo)
  Object.assign(buttonInfo.value, newButtonInfo)
}

onMounted(() => {
  // 初始化连接（若未连接）
  let userInfo = wsCache.get(CACHE_KEY.USER)
  callService.connect(userInfo)
  emitter.on(CallEvents.Raw, onRaw)
  emitter.on('ButtonStateChanged', onButtonStateChanged)
  listenEvent()
})

onBeforeUnmount(() => {
  emitter.off(CallEvents.Raw, onRaw)
  emitter.off('ButtonStateChanged', onButtonStateChanged)
  unlistenEvent()
})

// 选中订阅的服务端事件集合，用于 UI 同步
const listenEvent = () => {
  emitter.on(CCEvents.AgentLoggedOnEvt, onAgentLoggedOn)
  emitter.on(CCEvents.AgentLoggedOffEvt, onAgentLoggedOff)
  emitter.on(CCEvents.OriginatedEvt, onOriginated)
  emitter.on(CCEvents.DeliveredEvt, onDelivered)
  emitter.on(CCEvents.EstablishedEvt, onEstablished)
  emitter.on(CCEvents.ConnectionClearedEvt, onCleared)
  emitter.on(CCEvents.HeldEvt, onHeld)
  emitter.on(CCEvents.RetrievedEvt, onRetrieved)
  emitter.on(CCEvents.TransferedEvt, onTransfered)
  emitter.on(CCEvents.Error, onError)
}

const unlistenEvent = () => {
  emitter.off(CCEvents.AgentLoggedOnEvt, onAgentLoggedOn)
  emitter.off(CCEvents.AgentLoggedOffEvt, onAgentLoggedOff)
  emitter.off(CCEvents.OriginatedEvt, onOriginated)
  emitter.off(CCEvents.DeliveredEvt, onDelivered)
  emitter.off(CCEvents.EstablishedEvt, onEstablished)
  emitter.off(CCEvents.ConnectionClearedEvt, onCleared)
  emitter.off(CCEvents.HeldEvt, onHeld)
  emitter.off(CCEvents.RetrievedEvt, onRetrieved)
  emitter.off(CCEvents.TransferedEvt, onTransfered)
  emitter.off(CCEvents.Error, onError)
}
</script>

<style lang="scss" scoped>
.softbar {
  display: flex;
  align-items: center;
  padding-left: 5px; // 第一个元素和左边的间距稍微大一些

  // 去掉 dropdown 的边框
  .dropdown-no-border {
    :deep(.el-dropdown) {
      border: none !important;
      outline: none !important;
    }

    :deep(.el-button) {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;

      &:hover, &:focus {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }
    }
  }

  // 按钮间距调整
  > * {
    margin-right: 0.1px; // 按钮间距变小

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
