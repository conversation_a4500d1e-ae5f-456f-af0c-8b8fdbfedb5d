<template>
  <div class="softbar flex items-center h-full">
    <el-dropdown
      class="dropdown-no-border w-150px"
      @command="handleCodeSelect"
    >
      <el-button size="small" type="text">
        <span>{{ agentStatus }}</span>
        <span style="margin-left: 4px;">▼</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="item in codeList"
            :key="item.id"
            :command="Number(item.code)"
            :style="{ color: item.code === 0 ? '#409eff' : 'red' }"
          >
            {{ item.codeName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <el-input
      class="w-400px"
      placeholder="请输入号码"
      clearable
      v-model="callNumber"
      @keydown.enter="makeCall"
    />

    <el-button class="ml-10px" id="login_btn" @click="showLogin" v-if="buttonInfo.login_btn">登录</el-button>
    <el-button id="makeCall_btn" @click="makeCall" v-if="buttonInfo.makeCall_btn">外呼</el-button>
    <el-button id="hangup_btn" @click="hangUp" v-if="buttonInfo.hangup_btn">挂断</el-button>
    <el-button id="answer_btn" @click="answer" v-if="buttonInfo.answer_btn">接听</el-button>
    <el-button id="hold_btn" @click="hold" v-if="buttonInfo.hold_btn">保持</el-button>
    <el-button id="retrieve_btn" @click="retrieve" v-if="buttonInfo.retrieve_btn">取回</el-button>
    <el-button id="consult_btn" @click="consultBTN" v-if="buttonInfo.consult_btn">咨询</el-button>
    <el-button id="ssc_btn" @click="ssc" v-if="buttonInfo.ssc_btn">会议</el-button>
    <el-button id="sst_btn" @click="sst" v-if="buttonInfo.sst_btn">转移</el-button>
    <el-button id="alternate_btn" @click="alternate" v-if="buttonInfo.alternate_btn">切换</el-button>
    <el-button id="transfer_btn" @click="transfer" v-if="buttonInfo.transfer_btn">转移</el-button>
    <el-button id="reconnect_btn" @click="reconnect" v-if="buttonInfo.reconnect_btn">重连</el-button>
    <el-button id="conference_btn" @click="conference" v-if="buttonInfo.conference_btn">会议</el-button>
    <el-button id="sendDTMF_btn" @click="sendDTMF" v-if="buttonInfo.sendDTMF_btn">发送DTMF</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import callService from '@/services/call/CallService'
import { useEmitt } from '@/hooks/web/useEmitt'
import { useMessage } from '@/hooks/web/useMessage'
import { CallEvents, CCEvents, type CallEventPayload } from '@/services/call/types'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

interface CodeItem {
  id: number
  code: number
  codeName: string
}

// Call 状态枚举
enum CallStateType {
  DIALING = 'DIALING',
  RINGING = 'RINGING',
  CONNECTED = 'CONNECTED',
  HELD = 'HELD'
}

// Agent 状态枚举
enum AgentStateType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  READY = 'READY',
  NOT_READY = 'NOT_READY',
  WORK_NOT_READY = 'WORK_NOT_READY'
}

// Call 对象接口
interface CallInfo {
  callId: string
  createTime: number
  state: CallStateType
  direction: 'In' | 'Out'
  isHeld: boolean
  caller?: string
  callee?: string
  answerTime?: number
  queue?: string
  alertingDevice?: string
  callType?: 'Inbound' | 'Outbound' | 'Internal'
}

// Agent 对象接口
interface AgentInfo {
  state: AgentStateType
  lastState: AgentStateType
  lastTime: number
  loginTime?: number
  logoutTime?: number
  queues: string[]
}

// Calls 管理类
class CallsManager {
  callsList: CallInfo[] = []
  activeCall: string = ''
  consultationFrom: string = ''
  consultationTo: string = ''

  add(callInfo: Partial<CallInfo>) {
    const existingIndex = this.callsList.findIndex(call => call.callId === callInfo.callId)
    if (existingIndex >= 0) {
      // 更新现有通话
      this.callsList[existingIndex] = { ...this.callsList[existingIndex], ...callInfo }
    } else {
      // 添加新通话
      this.callsList.push(callInfo as CallInfo)
    }
  }

  get(callId: string): CallInfo | undefined {
    return this.callsList.find(call => call.callId === callId)
  }

  remove(callId: string) {
    this.callsList = this.callsList.filter(call => call.callId !== callId)
  }

  replace(oldCallId: string, newCallInfo: Partial<CallInfo>) {
    const index = this.callsList.findIndex(call => call.callId === oldCallId)
    if (index >= 0) {
      this.callsList[index] = { ...this.callsList[index], ...newCallInfo }
    }
  }

  filter(condition: Partial<CallInfo>): CallsManager {
    const filtered = new CallsManager()
    filtered.callsList = this.callsList.filter(call => {
      return Object.keys(condition).every(key => call[key] === condition[key])
    })
    return filtered
  }

  getLast(): CallInfo | undefined {
    return this.callsList[this.callsList.length - 1]
  }

  clearConsultation() {
    this.consultationFrom = ''
    this.consultationTo = ''
  }
}
const agentStatus = ref('未就绪')
const { wsCache } = useCache()
const callNumber = ref('')
const { emitter } = useEmitt()
const message = useMessage()
const codeList = ref<CodeItem[]>([])

// 按钮显示控制
const buttonInfo = ref({
  login_btn: true,
  sendDTMF_btn: false,
  makeCall_btn: false,
  hangup_btn: false,
  answer_btn: false,
  consult_btn: false,
  hold_btn: false,
  retrieve_btn: false,
  ssc_btn: false,
  sst_btn: false,
  alternate_btn: false,
  transfer_btn: false,
  reconnect_btn: false,
  conference_btn: false,
  validatePwd_btn: false
})

// 初始化 calls 管理器和 agent 信息
const calls = new CallsManager()
const agent = ref<AgentInfo>({
  state: AgentStateType.LOGOUT,
  lastState: AgentStateType.LOGOUT,
  lastTime: Date.now(),
  queues: []
})

// 站点信息
const stationId = ref('')
const srcDevice = ref('')
const domain = ref('')
const queueMap = ref(new Map())
const validate = ref('')

const handleCodeSelect = (code: number) => {
  console.log('选中的代码:', code)
  if(code == 0){
    agentStatus.value = '就绪'
    setAgentStatus(0,"Ready")
    message.notifySuccess('就绪成功')
  }else{
    const selectedItem = codeList.value.find((item: CodeItem) => item.code === code)
    if (selectedItem) {
      console.log('选中的项目:', selectedItem)
      agentStatus.value = selectedItem.codeName
      setAgentStatus(selectedItem.code,"NotReady")
      message.notifySuccess('状态切换成功')
    }
  }
  
}
const setAgentStatus = (reasonCode: any, agentStatus: any) => {
  let userInfo = wsCache.get(CACHE_KEY.USER)
  callService.setState(userInfo, reasonCode, agentStatus)
}

// 按钮方法定义
const showLogin = () => {
  console.log('登录')
  // 这里添加登录逻辑
}

const makeCall = () => {
  if (!callNumber.value) return
  console.log('外呼:', callNumber.value)
  callService.dial(callNumber.value)
}

const hangUp = () => {
  console.log('挂断')
  callService.hangup()
}

const answer = () => {
  console.log('接听')
  // 这里添加接听逻辑
}

const hold = () => {
  console.log('保持')
  // 这里添加保持逻辑
}

const retrieve = () => {
  console.log('取回')
  // 这里添加取回逻辑
}

const consultBTN = () => {
  console.log('咨询')
  // 这里添加咨询逻辑
}

const ssc = () => {
  console.log('会议')
  // 这里添加会议逻辑
}

const sst = () => {
  console.log('转移')
  // 这里添加转移逻辑
}

const alternate = () => {
  console.log('切换')
  // 这里添加切换逻辑
}

const transfer = () => {
  console.log('转移')
  if (!callNumber.value) return
  callService.transfer(callNumber.value)
}

const reconnect = () => {
  console.log('重连')
  // 这里添加重连逻辑
}

const conference = () => {
  console.log('会议')
  // 这里添加会议逻辑
}

const sendDTMF = () => {
  console.log('发送DTMF')
  // 这里添加发送DTMF逻辑
}

const onRaw = (s: CallEventPayload) => {
  console.log('onRaw', s)
  if(s.code === 201){
    const extraItem = {
      code: 0,
      codeName: '就绪',
      codeType: 0,
      domain: 'yihucc'
    }
    codeList.value = [extraItem, ...JSON.parse(s.message)]
    console.log('codeList', codeList.value)
  }
}
const checkButton = (data: CallEventPayload) => {

}
const onAgentStateChange = (data: CallEventPayload) => {
  console.log('onAgentStateChange', data)
  checkButton(data);
}

// ==================== CCEvents 事件处理方法 ====================

// 坐席登录事件
const onAgentLoggedOn = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onAgentLoggedOn', data)

  agent.value.lastState = agent.value.state
  agent.value.state = AgentStateType.LOGIN
  agent.value.lastTime = agent.value.loginTime = data.timestamp || Date.now()

  message.notifySuccess('坐席登录成功')

  // 更新按钮状态
  buttonInfo.value.login_btn = false
  buttonInfo.value.makeCall_btn = true
}

// 坐席退出事件
const onAgentLoggedOff = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onAgentLoggedOff', data)

  agent.value.lastState = agent.value.state
  agent.value.state = AgentStateType.LOGOUT
  agent.value.lastTime = agent.value.logoutTime = data.timestamp || Date.now()

  message.info('坐席已退出')

  // 重置按钮状态
  buttonInfo.value.login_btn = true
  buttonInfo.value.makeCall_btn = false
  buttonInfo.value.hangup_btn = false
  buttonInfo.value.answer_btn = false
}

// 坐席就绪事件
const onAgentReady = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onAgentReady', data)

  agent.value.lastState = agent.value.state
  agent.value.state = AgentStateType.READY
  agent.value.lastTime = data.timestamp || Date.now()

  agentStatus.value = '就绪'
}

// 坐席未就绪事件
const onAgentNotReady = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onAgentNotReady', data)

  agent.value.lastState = agent.value.state
  agent.value.state = AgentStateType.NOT_READY
  agent.value.lastTime = data.timestamp || Date.now()

  agentStatus.value = '未就绪'
}

// 坐席后处理事件
const onAgentWorkingAfterCall = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onAgentWorkingAfterCall', data)

  agent.value.lastState = agent.value.state
  agent.value.state = AgentStateType.WORK_NOT_READY
  agent.value.lastTime = data.timestamp || Date.now()

  agentStatus.value = '后处理'
}

// 摘机/外拨发起事件
const onOriginated = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onOriginated - 外拨', data)

  // 外拨时一定是激活的，外拨状态可以保持
  calls.activeCall = data.callId
  calls.add({
    callId: data.callId,
    state: CallStateType.DIALING,
    direction: 'Out',
    createTime: data.timestamp || Date.now(),
    isHeld: false,
    callType: 'Outbound'
  })

  // 更新按钮状态
  buttonInfo.value.makeCall_btn = false
  buttonInfo.value.hangup_btn = true

  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 振铃事件
const onDelivered = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onDelivered', data)

  // 来电时一定不是激活的，振铃状态不可以保持
  if (data.alertingDevice === stationId.value) {
    calls.add({
      callId: data.callId,
      createTime: data.timestamp || Date.now(),
      caller: data.callingDevice,
      callee: data.calledDevice,
      state: CallStateType.RINGING,
      direction: 'In',
      isHeld: false,
      queue: data.skill || '',
      alertingDevice: data.alertingDevice,
      callType: data.skill ? 'Inbound' : 'Internal'
    })

    // 更新按钮状态 - 来电
    buttonInfo.value.answer_btn = true
    buttonInfo.value.hangup_btn = true

    console.log('RINGING - 振铃')
  } else {
    // 外拨到达
    calls.add({
      callId: data.callId,
      state: CallStateType.DIALING,
      direction: 'Out',
      caller: data.callingDevice,
      callee: data.calledDevice,
      alertingDevice: data.alertingDevice
    })

    if (calls.consultationTo === data.alertingDevice) {
      calls.consultationTo = data.callId
    } else if (calls.callsList.length === 2 && calls.consultationFrom) {
      calls.consultationTo = data.callId
    }

    console.log('Delivered - 到达')
  }

  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 接通事件
const onEstablished = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onEstablished - 接通', data)

  let lastStateCall = calls.get(data.callId)
  // 之前存在，并且不是保持的
  if (!lastStateCall || !lastStateCall.isHeld) {
    calls.activeCall = data.callId
  }

  calls.add({
    callId: data.callId,
    state: CallStateType.CONNECTED,
    answerTime: data.timestamp || Date.now()
  })

  // 更新按钮状态 - 通话中
  buttonInfo.value.answer_btn = false
  buttonInfo.value.hold_btn = true
  buttonInfo.value.transfer_btn = true
  buttonInfo.value.conference_btn = true

  console.log('Established - 接通')
  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 挂断事件
const onCleared = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onConnectionCleared - 挂断', data)

  // 自己挂断
  if (stationId.value === data.releasingDevice) {
    // 清除磋商标记
    if (data.callId === calls.consultationFrom || data.callId === calls.consultationTo) {
      calls.clearConsultation()
    }

    calls.remove(data.callId)
    if (data.callId === calls.activeCall) {
      calls.activeCall = ''
    }

    let activeCall = calls.filter({ isHeld: false }).getLast()
    if (activeCall && !calls.get(calls.activeCall)) {
      calls.activeCall = activeCall.callId
    }

    // 触发通话结束事件
    emitter.emit('CallEnd', {})

    // 更新按钮状态 - 通话结束
    if (calls.callsList.length === 0) {
      buttonInfo.value.hangup_btn = false
      buttonInfo.value.answer_btn = false
      buttonInfo.value.hold_btn = false
      buttonInfo.value.retrieve_btn = false
      buttonInfo.value.transfer_btn = false
      buttonInfo.value.conference_btn = false
      buttonInfo.value.makeCall_btn = true
    }
  } else if ('Fail' === data.connectionState) {
    // 连接失败，暂不处理
  }

  console.log('ConnectionCleared - 挂断')
  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 保持事件
const onHeld = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onHeld - 保持', data)

  // 已激活的callId并且是保持方
  if (data.holdingDevice === stationId.value) {
    calls.add({
      callId: data.callId,
      isHeld: true
    })
  }

  if (data.callId === calls.activeCall) {
    calls.activeCall = ''
  }

  // 更新按钮状态
  buttonInfo.value.hold_btn = false
  buttonInfo.value.retrieve_btn = true

  console.log('Held - 保持')
  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 恢复事件
const onRetrieved = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onRetrieved - 恢复', data)

  // 已保持的callId并且是恢复方
  if (data.retrievingDevice === stationId.value) {
    calls.replace(data.callId, {
      callId: data.callId,
      isHeld: false
    })

    if (calls.callsList.length) {
      calls.activeCall = data.callId
    }

    // 更新按钮状态
    buttonInfo.value.hold_btn = true
    buttonInfo.value.retrieve_btn = false

    console.log('Retrieved - 恢复')
    console.log('activeCall:', calls.activeCall)
    console.log('calls:', JSON.stringify(calls.callsList))
  }
}

// 会议事件
const onConferenced = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onConferenced - 会议', data)

  let clearCall = calls.consultationTo === data.newCall ? calls.consultationFrom : calls.consultationTo
  calls.clearConsultation()
  calls.remove(clearCall)
  calls.replace(calls.getLast()?.callId || '', {
    callId: data.newCall,
    isHeld: false,
    state: CallStateType.CONNECTED
  })
  calls.activeCall = data.newCall

  console.log('Conferenced - 会议')
  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 转移事件
const onTransfered = (data: CallEventPayload) => {
  if (data.srcDevice !== srcDevice.value) return
  console.log('onTransfered - 转移', data)

  if (data.primaryOldCall === calls.consultationFrom || data.secondaryOldCall === calls.consultationFrom
      || data.primaryOldCall === calls.consultationTo || data.secondaryOldCall === calls.consultationTo) {
    calls.clearConsultation()
  }

  // 自己是转移方
  if (data.transferringDevice === stationId.value && data.transferringDevice !== data.transferredToDevice) {
    calls.activeCall = ''
    calls.remove(data.primaryOldCall)
    calls.remove(data.secondaryOldCall)

    // 更新按钮状态 - 转移完成
    buttonInfo.value.hangup_btn = false
    buttonInfo.value.transfer_btn = false
    buttonInfo.value.conference_btn = false
    buttonInfo.value.makeCall_btn = true
  } else {
    let existCall: string
    if (calls.get(data.primaryOldCall)) {
      existCall = data.primaryOldCall
    } else {
      existCall = data.secondaryOldCall
    }

    // 更新旧的callId，旧的电话为激活则更新激活的电话
    calls.replace(existCall, {
      callId: data.newCall,
    })
    if (calls.activeCall) {
      calls.activeCall = data.newCall
    }
  }

  console.log('Transferred - 转移')
  console.log('activeCall:', calls.activeCall)
  console.log('calls:', JSON.stringify(calls.callsList))
}

// 排队事件
const onQueueCallChange = (data: CallEventPayload) => {
  console.log('onQueueCallChange', data)
  queueMap.value.set(data.skill, data.count)
  if (!agent.value.queues.includes(data.skill)) {
    agent.value.queues.push(data.skill)
  }
}

// 密码验证事件
const onValidatePwd = (data: CallEventPayload) => {
  console.log('onValidatePwd', data)
  validate.value = data.validatePwd
  console.log('validate password result:', data.validatePwd)
}

// 错误事件
const onError = (data: CallEventPayload) => {
  console.log('onError', data)
  message.error('呼叫系统错误: ' + (data.message || '未知错误'))
}

onMounted(() => {
  // 初始化连接（若未连接）
  let userInfo = wsCache.get(CACHE_KEY.USER)

  // 设置设备信息
  if (userInfo) {
    stationId.value = userInfo.stationId || userInfo.username
    srcDevice.value = userInfo.srcDevice || userInfo.username
    domain.value = userInfo.domain || 'default'
  }

  callService.connect(userInfo)
  emitter.on(CallEvents.Raw, onRaw)
  listenEvent()
})

onBeforeUnmount(() => {
  emitter.off(CallEvents.Raw, onRaw)
  unlistenEvent()
})

// 选中订阅的服务端事件集合，用于 UI 同步
const listenEvent = () => {
  emitter.on(CCEvents.AgentStateChangeEvt, onAgentStateChange)
  emitter.on(CCEvents.AgentLoggedOnEvt, onAgentLoggedOn)
  emitter.on(CCEvents.AgentLoggedOffEvt, onAgentLoggedOff)
  emitter.on(CCEvents.AgentReadyEvt, onAgentReady)
  emitter.on(CCEvents.AgentNotReadyEvt, onAgentNotReady)
  emitter.on(CCEvents.AgentWorkingAfterCallEvt, onAgentWorkingAfterCall)
  emitter.on(CCEvents.OriginatedEvt, onOriginated)
  emitter.on(CCEvents.DeliveredEvt, onDelivered)
  emitter.on(CCEvents.EstablishedEvt, onEstablished)
  emitter.on(CCEvents.ConnectionClearedEvt, onCleared)
  emitter.on(CCEvents.HeldEvt, onHeld)
  emitter.on(CCEvents.RetrievedEvt, onRetrieved)
  emitter.on(CCEvents.ConferencedEvt, onConferenced)
  emitter.on(CCEvents.TransferedEvt, onTransfered)
  emitter.on(CCEvents.QueueCallChangeEvt, onQueueCallChange)
  emitter.on(CCEvents.ValidatePwdEvt, onValidatePwd)
  emitter.on(CCEvents.Error, onError)
}

const unlistenEvent = () => {
  emitter.off(CCEvents.AgentStateChangeEvt, onAgentStateChange)
  emitter.off(CCEvents.AgentLoggedOnEvt, onAgentLoggedOn)
  emitter.off(CCEvents.AgentLoggedOffEvt, onAgentLoggedOff)
  emitter.off(CCEvents.AgentReadyEvt, onAgentReady)
  emitter.off(CCEvents.AgentNotReadyEvt, onAgentNotReady)
  emitter.off(CCEvents.AgentWorkingAfterCallEvt, onAgentWorkingAfterCall)
  emitter.off(CCEvents.OriginatedEvt, onOriginated)
  emitter.off(CCEvents.DeliveredEvt, onDelivered)
  emitter.off(CCEvents.EstablishedEvt, onEstablished)
  emitter.off(CCEvents.ConnectionClearedEvt, onCleared)
  emitter.off(CCEvents.HeldEvt, onHeld)
  emitter.off(CCEvents.RetrievedEvt, onRetrieved)
  emitter.off(CCEvents.ConferencedEvt, onConferenced)
  emitter.off(CCEvents.TransferedEvt, onTransfered)
  emitter.off(CCEvents.QueueCallChangeEvt, onQueueCallChange)
  emitter.off(CCEvents.ValidatePwdEvt, onValidatePwd)
  emitter.off(CCEvents.Error, onError)
}
</script>

<style lang="scss" scoped>
.softbar {
  display: flex;
  align-items: center;
  padding-left: 5px; // 第一个元素和左边的间距稍微大一些

  // 去掉 dropdown 的边框
  .dropdown-no-border {
    :deep(.el-dropdown) {
      border: none !important;
      outline: none !important;
    }

    :deep(.el-button) {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;

      &:hover, &:focus {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }
    }
  }

  // 按钮间距调整
  > * {
    margin-right: 0.1px; // 按钮间距变小

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
