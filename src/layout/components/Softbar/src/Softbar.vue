<template>
  <div class="softbar flex items-center h-full">
    <el-dropdown
      class="dropdown-no-border w-150px"
      @command="handleCodeSelect"
    >
      <el-button size="small" type="text">
        <span>{{ getSelectedCodeName() }}</span>
        <span style="margin-left: 4px;">▼</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="item in codeList"
            :key="item.id"
            :command="item.code"
          >
            {{ item.codeName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <el-input
      class="w-400px"
      placeholder="请输入号码"
      clearable
      v-model="dialNumber"
      @keydown.enter="handleCall"
    />

    <el-button class="ml-[10px]" type="primary" @click="handleCall">呼叫</el-button>
    <el-button type="danger" @click="handleHangup">挂断</el-button>
    <el-button @click="handleTransfer">转接</el-button>
    <el-button @click="handleMute">静音</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import callService from '@/services/call/CallService'
import { useEmitt } from '@/hooks/web/useEmitt'
import { useMessage } from '@/hooks/web/useMessage'
import { CallEvents, CCEvents, type CallEventPayload } from '@/services/call/types'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()
const dialNumber = ref('')
const { emitter } = useEmitt()
const message = useMessage()
const codeList = ref([])
const selectedCode = ref<string | null>(null)

const handleCall = () => {
  if (!dialNumber.value) return
  callService.dial(dialNumber.value)
}

const handleHangup = () => {
  callService.hangup()
}

const handleTransfer = () => {
  // 这里可以拓展为弹出输入或选择联系人
  // 简化为转接到当前输入框号码
  if (!dialNumber.value) return
  callService.transfer(dialNumber.value)
}

const handleMute = () => {
  callService.mute()
}

const handleCodeSelect = (code: string) => {
  selectedCode.value = code
}

const getSelectedCodeName = () => {
  if (!selectedCode.value) return '未就绪'
  const item = codeList.value.find((item: any) => item.code === selectedCode.value)
  return item ? item.codeName : '未就绪'
}

const onRaw = (s: CallEventPayload) => {
  console.log('onRaw', s)
  if(s.code === 201){
    codeList.value = JSON.parse(s.message)
    console.log('codeList', codeList.value)
  }
}

// 选中订阅的服务端事件集合，用于 UI 同步
const listenEvent = () => {
  emitter.on(CCEvents.AgentLoggedOnEvt, onAgentLoggedOn)
  emitter.on(CCEvents.AgentLoggedOffEvt, onAgentLoggedOff)
  emitter.on(CCEvents.OriginatedEvt, onOriginated)
  emitter.on(CCEvents.DeliveredEvt, onDelivered)
  emitter.on(CCEvents.EstablishedEvt, onEstablished)
  emitter.on(CCEvents.ConnectionClearedEvt, onCleared)
  emitter.on(CCEvents.HeldEvt, onHeld)
  emitter.on(CCEvents.RetrievedEvt, onRetrieved)
  emitter.on(CCEvents.TransferedEvt, onTransfered)
  emitter.on(CCEvents.Error, onError)
}

const unlistenEvent = () => {
  emitter.off(CCEvents.AgentLoggedOnEvt, onAgentLoggedOn)
  emitter.off(CCEvents.AgentLoggedOffEvt, onAgentLoggedOff)
  emitter.off(CCEvents.OriginatedEvt, onOriginated)
  emitter.off(CCEvents.DeliveredEvt, onDelivered)
  emitter.off(CCEvents.EstablishedEvt, onEstablished)
  emitter.off(CCEvents.ConnectionClearedEvt, onCleared)
  emitter.off(CCEvents.HeldEvt, onHeld)
  emitter.off(CCEvents.RetrievedEvt, onRetrieved)
  emitter.off(CCEvents.TransferedEvt, onTransfered)
  emitter.off(CCEvents.Error, onError)
}

const onAgentLoggedOn = (data: CallEventPayload) => {
  selectedCode.value = null
  console.log('onAgentLoggedOn', data)
  message.notifySuccess('坐席登录成功')
}

const onAgentLoggedOff = () => {}
const onOriginated = () => {}
const onDelivered = () => {}
const onEstablished = () => {}
const onCleared = () => {}
const onHeld = () => {}
const onRetrieved = () => {}
const onTransfered = () => {}
const onError = () => {}

onMounted(() => {
  // 初始化连接（若未连接）
  let userInfo = wsCache.get(CACHE_KEY.USER)
  callService.connect(userInfo)
  emitter.on(CallEvents.Raw, onRaw)
  listenEvent()
})

onBeforeUnmount(() => {
  emitter.off(CallEvents.Raw, onRaw)
  unlistenEvent()
})
</script>

<style lang="scss" scoped>
.softbar {
  display: flex;
  align-items: center;
  padding-left: 5px; // 第一个元素和左边的间距稍微大一些

  // 去掉 dropdown 的边框
  .dropdown-no-border {
    :deep(.el-dropdown) {
      border: none !important;
      outline: none !important;
    }

    :deep(.el-button) {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;

      &:hover, &:focus {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }
    }
  }

  // 按钮间距调整
  > * {
    margin-right: 0.1px; // 按钮间距变小

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
