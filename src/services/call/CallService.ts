import { useEmitt } from '@/hooks/web/useEmitt'
import type { CallStatus, CallEventPayload } from './types'
import { CallEvents } from './types'

class CallService {
  private ws: WebSocket | null = null
  private status: CallStatus = 'IDLE'
  private isMuted = false
  private readonly emitter = useEmitt().emitter

  get currentStatus(): CallStatus {
    return this.status
  }

  get connected(): boolean {
    return !!this.ws && this.ws.readyState === WebSocket.OPEN
  }

  connect(userInfo: any,url?: string) {
    console.log('ws userInfo',userInfo)
    const wsUrl = url || (import.meta.env.VITE_CALL_WS_URL as string)
    if (!wsUrl) {
      console.warn('CallService: VITE_CALL_WS_URL is not set, skip connect')
      return
    }
    if (this.connected) return

    this.ws = new WebSocket(wsUrl)
    this.ws.onopen = () => {
      this.emitter.emit(CallEvents.WsOpen)
      this.signIn(userInfo)
    }
    this.ws.onclose = () => {
      this.emitter.emit(CallEvents.WsClose)
      this.ws = null
      this.setStatus('IDLE')
    }
    this.ws.onerror = (e) => {
      this.emitter.emit(CallEvents.WsError, e)
    }
    this.ws.onmessage = (evt: MessageEvent) => {
      try {
        const data: CallEventPayload = JSON.parse(evt.data)
        console.log('ws data',data)
        if (data && data.evtName) {
          this.emitter.emit(data.evtName, data)
          this.mapEventToStatus(data.evtName)
        }
        this.emitter.emit(CallEvents.Raw, data)
      } catch (err) {
        console.warn('CallService: invalid message', evt.data)
      }
    }
  }

  signIn(userInfo: any) {
    // {"stationId":"2002","agentId":"6002","acdId":"101","domain":"yihucc","agentState":"Login","reasonCode":0,"workMode":"AutoIn","type":"1"}
    const info = {
      stationId: userInfo.agent.stationNum,
      agentId: userInfo.agent.agentNum,
      agentPwd: userInfo.agent.password,
      acdId: '',
      domain: userInfo.user.tenantName,
      agentState: 'Login',
      reasonCode: 0,
      workMode: 'AutoIn',
      type: "1"
    }
    this.send(info)
  }

  setState(userInfo,reasonCode, agentState) {
      const info = {
          stationId: userInfo.agent.stationNum,
          agentId: userInfo.agent.agentNum,
          acdId: '',
          domain: userInfo.user.tenantName,
          agentState: agentState,
          reasonCode: reasonCode,
          workMode: 'AutoIn',
          type: "17"
      }
      this.send(info)
    }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  dial(number: string) {
    this.ensure()
    // TODO: align payload with backend protocol
    this.send({ action: 'dial', number })
    this.setStatus('DIALING')
  }

  hangup() {
    this.ensure()
    this.send({ action: 'hangup' })
    this.setStatus('ENDED')
  }

  transfer(to: string) {
    this.ensure()
    this.send({ action: 'transfer', to })
  }

  mute(toggle?: boolean) {
    this.ensure()
    const next = toggle ?? !this.isMuted
    this.isMuted = next
    this.send({ action: 'mute', enabled: next })
  }

  private ensure() {
    if (!this.connected) this.connect()
  }

  private send(payload: Record<string, unknown>) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('CallService: WebSocket not connected')
      return
    }
    this.ws.send(JSON.stringify(payload))
  }

  private mapEventToStatus(evtName: string) {
    switch (evtName) {
      case 'OriginatedEvt':
        this.setStatus('DIALING')
        break
      case 'DeliveredEvt':
        this.setStatus('RINGING')
        break
      case 'EstablishedEvt':
        this.setStatus('CONNECTED')
        break
      case 'ConnectionClearedEvt':
        this.setStatus('ENDED')
        break
      case 'HeldEvt':
        this.setStatus('HELD')
        break
      default:
        break
    }
  }

  private setStatus(next: CallStatus) {
    if (this.status === next) return
    this.status = next
    this.emitter.emit(CallEvents.StatusChanged, next)
  }
}

export const callService = new CallService()
export default callService 