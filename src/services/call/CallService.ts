import { useEmitt } from '@/hooks/web/useEmitt'
import type { CallStatus, CallEventPayload } from './types'
import { CallEvents, CCEvents } from './types'

// Call 状态枚举
enum CallStateType {
  DEAD = 'DEAD',
  DIALING = 'DIALING',
  RINGING = 'RINGING',
  CONNECTED = 'CONNECTED',
  HELD = 'HELD'
}

// Agent 状态枚举
enum AgentStateType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  READY = 'READY',
  NOT_READY = 'NOT_READY',
  WORK_NOT_READY = 'WORK_NOT_READY'
}

// Call 类
class Call {
  callId: string = ''
  state: CallStateType = CallStateType.DEAD
  createTime: number = 0
  answerTime: number = 0
  caller: string = ''
  callee: string = ''
  callType: string = ''
  alertingDevice: string = ''
  direction: string = ''
  isHeld: boolean = false
  queue: string = ''
}

// Calls 管理类
class Calls {
  activeCall: string = ''
  consultationType: string = ''
  consultationFrom: string = ''
  consultationTo: string = ''
  callsList: Call[] = []

  get(callId: string): Call | undefined {
    return this.callsList.find(item => item.callId === callId)
  }

  add(call: Partial<Call>) {
    if (call.callId) {
      let origCall = this.get(call.callId)
      if (!origCall) {
        origCall = new Call()
        this.callsList.push(origCall)
      }
      // 更新属性
      Object.keys(origCall).forEach(key => {
        if (call[key] !== undefined) {
          origCall[key] = call[key]
        }
      })
    }
  }

  remove(callId: string) {
    for (let i = 0, len = this.callsList.length; i < len; i++) {
      if (this.callsList[i].callId === callId) {
        this.callsList.splice(i, 1)
        return
      }
    }
  }

  removeIndex(index: number) {
    this.callsList.splice(index, 1)
  }

  clear() {
    this.callsList.length = 0
  }

  clearConsultation() {
    this.consultationFrom = ''
    this.consultationTo = ''
    this.consultationType = ''
  }

  getIndex(index: number): Call | null {
    return this.callsList[index] || null
  }

  getLast(): Call | null {
    return this.callsList[this.callsList.length - 1] || null
  }

  indexOf(callId: string): number {
    for (let i = 0, len = this.callsList.length; i < len; i++) {
      if (this.callsList[i].callId === callId) return i
    }
    return -1
  }

  size(): number {
    return this.callsList.length
  }

  replace(callId: string, call: Partial<Call>) {
    const origCall = this.get(callId)
    if (origCall) {
      Object.keys(origCall).forEach(key => {
        if (call[key] !== undefined) {
          origCall[key] = call[key]
        }
      })
    }
  }

  filter(condition: Partial<Call>): Calls {
    const calls = new Calls()
    // 命名循环
    callsLoop: for (let i = 0, len = this.callsList.length; i < len; i++) {
      for (const key in condition) {
        if (this.callsList[i][key] === undefined || this.callsList[i][key] !== condition[key]) {
          continue callsLoop
        }
      }
      calls.callsList.push(this.callsList[i])
    }
    return calls
  }
}

// Agent 类
class Agent {
  agentId: string = ''
  state: AgentStateType = AgentStateType.LOGOUT
  lastState: AgentStateType = AgentStateType.LOGOUT
  lastTime: number = Date.now()
  loginTime?: number
  logoutTime?: number
  queues: string[] = []
}

class CallService {
  private ws: WebSocket | null = null
  private status: CallStatus = 'IDLE'
  private isMuted = false
  private readonly emitter = useEmitt().emitter

  // Station 相关属性
  private stationId: string = ''
  private srcDevice: string = ''
  private domain: string = ''
  private loginMode: string = ''
  private calls: Calls = new Calls()
  private agent: Agent = new Agent()
  private queueMap: Map<string, number> = new Map()
  private initialized: boolean = false
  private acdId: string = '101'
  private validate: string = ''

  get currentStatus(): CallStatus {
    return this.status
  }

  get connected(): boolean {
    return !!this.ws && this.ws.readyState === WebSocket.OPEN
  }

  // 获取通话信息
  get callsInfo(): Calls {
    return this.calls
  }

  // 获取坐席信息
  get agentInfo(): Agent {
    return this.agent
  }

  // 获取当前激活通话
  get activeCallId(): string {
    return this.calls.activeCall
  }

  // 获取所有通话列表
  get callsList(): Call[] {
    return this.calls.callsList
  }

  connect(userInfo: any, url?: string) {
    console.log('ws userInfo', userInfo)
    const wsUrl = url || (import.meta.env.VITE_CALL_WS_URL as string)
    if (!wsUrl) {
      console.warn('CallService: VITE_CALL_WS_URL is not set, skip connect')
      return
    }
    if (this.connected) return

    // 初始化 Station
    this.initStation(userInfo)

    this.ws = new WebSocket(wsUrl)
    this.ws.onopen = () => {
      this.emitter.emit(CallEvents.WsOpen)
      this.signIn(userInfo)
    }
    this.ws.onclose = () => {
      this.emitter.emit(CallEvents.WsClose)
      this.ws = null
      this.setStatus('IDLE')
    }
    this.ws.onerror = (e) => {
      this.emitter.emit(CallEvents.WsError, e)
    }
    this.ws.onmessage = (evt: MessageEvent) => {
      try {
        const data: CallEventPayload = JSON.parse(evt.data)
        // console.log('ws data', data)
        if (data && data.evtName) {
          // 处理 CCEvents 中的事件
          this.handleCCEvent(data)
          this.emitter.emit(data.evtName, data)
          this.mapEventToStatus(data.evtName)
        }
        this.emitter.emit(CallEvents.Raw, data)
      } catch (err) {
        console.warn('CallService: invalid message', evt.data)
      }
    }
  }

  private initStation(userInfo: any) {
    if (this.initialized) return

    // 设置设备信息
    if (userInfo) {
      this.stationId = userInfo.agent?.stationNum
      this.srcDevice = userInfo.agent?.stationNum + ":" + userInfo.user.tenantName
      this.domain = userInfo.user?.tenantName || 'default'
    }

    this.initialized = true
  }

  signIn(userInfo: any) {
    // {"stationId":"2002","agentId":"6002","acdId":"101","domain":"yihucc","agentState":"Login","reasonCode":0,"workMode":"AutoIn","type":"1"}

    // 设置 agentId
    this.agent.agentId = userInfo.agent?.agentNum || ''

    const info = {
      stationId: userInfo.agent.stationNum,
      agentId: userInfo.agent.agentNum,
      agentPwd: userInfo.agent.password,
      acdId: '',
      domain: userInfo.user.tenantName,
      agentState: 'Login',
      reasonCode: 0,
      workMode: 'AutoIn',
      type: "1"
    }
    this.send(info)
  }

  setState(userInfo: any, reasonCode: any, agentState: any) {
    const info = {
      stationId: userInfo.agent.stationNum,
      agentId: userInfo.agent.agentNum,
      acdId: '',
      domain: userInfo.user.tenantName,
      agentState: agentState,
      reasonCode: reasonCode,
      workMode: 'AutoIn',
      type: "17"
    }
    this.send(info)
  }

  // 处理 CCEvents 中的事件
  private handleCCEvent(data: CallEventPayload) {
    if (!data.evtName) return

    switch (data.evtName) {
      case CCEvents.AgentStateChangeEvt:
        this.onAgentStateChange(data)
        break
      case CCEvents.StationStateChangeEvt:
        this.onStationStateChange(data)
      case CCEvents.AgentLoggedOnEvt:
        this.onAgentLoggedOn(data)
        break
      case CCEvents.AgentLoggedOffEvt:
        this.onAgentLoggedOff(data)
        break
      case CCEvents.AgentReadyEvt:
        this.onAgentReady(data)
        break
      case CCEvents.AgentNotReadyEvt:
        this.onAgentNotReady(data)
        break
      case CCEvents.AgentWorkingAfterCallEvt:
        this.onAgentWorkingAfterCall(data)
        break
      case CCEvents.OriginatedEvt:
        this.onOriginated(data)
        break
      case CCEvents.DeliveredEvt:
        this.onDelivered(data)
        break
      case CCEvents.EstablishedEvt:
        this.onEstablished(data)
        break
      case CCEvents.ConnectionClearedEvt:
        this.onConnectionCleared(data)
        break
      case CCEvents.HeldEvt:
        this.onHeld(data)
        break
      case CCEvents.RetrievedEvt:
        this.onRetrieved(data)
        break
      case CCEvents.ConferencedEvt:
        this.onConferenced(data)
        break
      case CCEvents.TransferedEvt:
        this.onTransfered(data)
        break
      case CCEvents.QueueCallChangeEvt:
        this.onQueueCallChange(data)
        break
      case CCEvents.ValidatePwdEvt:
        this.onValidatePwd(data)
        break
      default:
        break
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  dial(number: string) {
    this.ensure()
    // TODO: align payload with backend protocol
    this.send({ action: 'dial', number })
    this.setStatus('DIALING')
  }

  // ==================== 通话控制方法 ====================

  makeCall(dest: string, options: any = {}) {
    console.log('station - makeCall')
    // 有电话并且不是摘机状态返回
    if (this.calls.callsList.length > 2 || this.calls.callsList.length === 1) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 外拨
    return this.send({
      agentId: this.agent.agentId || '',
      stationId: this.stationId,
      caller: this.stationId,
      callee: dest,
      domain: this.domain,
      uui: options.uui,
      businessCode: options.businessCode || '003',
      type: '2'
    })
  }

  answer() {
    console.log('station - answer')
    // 振铃列表为空则返回
    const ringingCall = this.calls.filter({
      state: CallStateType.RINGING
    }).getLast()

    if (!ringingCall) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 保存CallId，防止改变
    const ringingCallId = ringingCall.callId

    // 已有激活的电话
    if (this.calls.activeCall) {
      // 先保持
      return this.hold().then(() => {
        return this.send({
          uuid: ringingCallId,
          device: this.stationId,
          domain: this.domain,
          type: '6'
        })
      })
    } else {
      return this.send({
        uuid: ringingCallId,
        device: this.stationId,
        domain: this.domain,
        type: '6'
      })
    }
  }

  hangup() {
    console.log('station - hangup')
    const heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    const initiatedCall = this.calls.filter({
      state: CallStateType.DIALING // 使用 DIALING 替代 OFF_HOOK
    }).getLast()

    // 电话全是振铃状态则返回
    if (!this.calls.callsList.length ||
        this.calls.filter({ state: CallStateType.RINGING }).callsList.length === this.calls.callsList.length) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 有激活的或摘机的先挂
    const activeCall = this.calls.get(this.calls.activeCall)
    if (activeCall || initiatedCall) {
      // 发送消息
      return this.send({
        uuid: (activeCall && activeCall.callId) || (initiatedCall && initiatedCall.callId),
        device: this.stationId,
        domain: this.domain,
        type: '5'
      })
    } else if (heldCall) {
      // 保存CallId，防止改变
      const callId = heldCall.callId
      // 先恢复
      return this.retrieve().then(() => {
        // 发送消息
        return this.send({
          uuid: callId,
          device: this.stationId,
          domain: this.domain,
          type: '5'
        })
      })
    }

    return Promise.reject(new Error('INVALID_STATE_ERR'))
  }

  hold() {
    console.log('station - hold')
    // 没有已激活的电话
    const activeCall = this.calls.get(this.calls.activeCall)
    if (!activeCall) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      uuid: activeCall.callId,
      device: this.stationId,
      domain: this.domain,
      type: '3'
    })
  }

  retrieve() {
    console.log('station - retrieve')
    // 有激活的电话重连
    // 有保持的电话恢复
    // 无保持的则返回
    const heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    const initiatedCall = this.calls.filter({
      state: CallStateType.DIALING // 使用 DIALING 替代 OFF_HOOK
    }).getLast()

    if (!heldCall) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    if (this.calls.activeCall) {
      return this.reconnect()
    } else {
      if (initiatedCall) {
        return this.send({
          uuid: initiatedCall.callId,
          device: this.stationId,
          domain: this.domain,
          type: '5'
        }).then(() => {
          // 发送消息
          return this.send({
            uuid: heldCall.callId,
            device: this.stationId,
            domain: this.domain,
            type: '4'
          })
        })
      } else {
        // 发送消息
        return this.send({
          uuid: heldCall.callId,
          device: this.stationId,
          domain: this.domain,
          type: '4'
        })
      }
    }
  }

  transfer(to: string) {
    this.ensure()
    this.send({ action: 'transfer', to })
  }

  reconnect() {
    console.log('station - reconnect')
    const activeCall = this.calls.get(this.calls.activeCall)
    const heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    // 发送消息
    if (!activeCall || !heldCall) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    return this.send({
      activeCall: activeCall.callId,
      holdCall: heldCall.callId,
      device: this.stationId,
      domain: this.domain,
      type: '12'
    })
  }

  alternate() {
    console.log('station - alternate')
    const activeCall = this.calls.get(this.calls.activeCall)
    const heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    // 发送消息
    if (!activeCall || !heldCall) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    return this.send({
      activeCall: activeCall.callId,
      holdCall: heldCall.callId,
      device: this.stationId,
      domain: this.domain,
      type: '11'
    })
  }

  consultation(dest: string, options: any = {}) {
    console.log('station - consultation')
    const activeCall = this.calls.get(this.calls.activeCall)
    const heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    if (!this.calls.callsList.length ||
        (activeCall && CallStateType.CONNECTED !== activeCall.state) ||
        (!activeCall && !heldCall) ||
        (heldCall && heldCall.state !== CallStateType.CONNECTED)) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 没有已激活的或者已激活的不是通话状态 或者 没有保持的或者保持的不是通话状态 问题场景屏蔽
    const existingCall = activeCall || heldCall
    if (!existingCall) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      uuid: existingCall.callId,
      agentId: this.agent.agentId,
      device: this.stationId,
      domain: this.domain,
      consultDevice: dest,
      businessCode: options.businessCode || '',
      uui: this.encodeUui(options.uui),
      type: '10'
    }).then(() => {
      this.calls.consultationFrom = existingCall.callId
      this.calls.consultationTo = dest
      this.calls.consultationType = options.type || ''
    })
  }

  conference(options: any = {}) {
    console.log('station - conference')
    // 没有保持的或激活的电话则返回
    // 已保持的电话必须处于通话状态
    let activeCall = this.calls.get(this.calls.activeCall)
    let heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    const consultationFrom = this.calls.get(this.calls.consultationFrom)
    // 修正磋商响应事件发生在振铃事件之后导致consultationTo为空的情形
    const consultationTo = this.calls.get(this.calls.consultationTo)
    this.calls.consultationType = this.calls.consultationType || ''
    const consultationType = options.type || ''

    if (consultationFrom && consultationTo) {
      // consultationFrom 或 consultationTo 其中一个状态是保持，另一个是激活
      if (consultationFrom.isHeld && (consultationTo.state === CallStateType.CONNECTED || consultationTo.state === CallStateType.DIALING)) {
        activeCall = consultationTo
        heldCall = consultationFrom
      } else if ((consultationFrom.state === CallStateType.CONNECTED || consultationFrom.state === CallStateType.DIALING) && consultationTo.isHeld) {
        activeCall = consultationFrom
        heldCall = consultationTo
      } else {
        return Promise.reject(new Error('INVALID_STATE_ERR'))
      }
    }

    // 如果没有激活的电话 或者磋商类型与会议类型不一致 或者磋商的电话不存在 或者磋商的电话不是保持状态
    // 或者 磋商的电话不是通话状态 则返回
    if (!activeCall || !heldCall || this.calls.consultationType !== consultationType) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      activeCall: activeCall.callId,
      holdCall: heldCall.callId,
      device: this.stationId,
      domain: this.domain,
      type: '14'
    })
  }

  transferCall(options: any = {}) {
    console.log('station - transfer')
    let activeCall = this.calls.get(this.calls.activeCall)
    let heldCall = this.calls.filter({
      isHeld: true
    }).getLast()

    const consultationFrom = this.calls.get(this.calls.consultationFrom)
    // 修正磋商响应事件发生在振铃事件之后导致consultationTo为空的情形
    const consultationTo = this.calls.get(this.calls.consultationTo)
    this.calls.consultationType = this.calls.consultationType || ''
    const consultationType = options.type || ''

    if (consultationFrom && consultationTo) {
      // consultationFrom 或 consultationTo 其中一个状态是保持，另一个是激活
      if (consultationFrom.isHeld && (consultationTo.state === CallStateType.CONNECTED || consultationTo.state === CallStateType.DIALING)) {
        activeCall = consultationTo
        heldCall = consultationFrom
      } else if ((consultationFrom.state === CallStateType.CONNECTED || consultationFrom.state === CallStateType.DIALING) && consultationTo.isHeld) {
        activeCall = consultationFrom
        heldCall = consultationTo
      } else {
        return Promise.reject(new Error('INVALID_STATE_ERR'))
      }
    }

    // 如果没有激活的电话 或者磋商类型与会议类型不一致 或者磋商的电话不存在 或者磋商的电话不是保持状态
    // 或者 磋商的电话不是通话状态 则返回
    if (!activeCall || !heldCall || this.calls.consultationType !== consultationType) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      activeCall: activeCall.callId,
      holdCall: heldCall.callId,
      device: this.stationId,
      activeDevice: activeCall.callee, // 被咨询者
      domain: this.domain,
      type: '13'
    })
  }

  singleStepConference(dest: string) {
    console.log('station - singleStepConference')
    // 没有激活的电话或者激活的电话不是通话状态则返回
    const activeCall = this.calls.get(this.calls.activeCall)
    if (!activeCall || activeCall.state !== CallStateType.CONNECTED) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      uuid: activeCall.callId,
      deviceTo: dest,
      device: this.stationId,
      domain: this.domain,
      type: '8'
    })
  }

  singleStepTransfer(dest: string, options: any = {}) {
    console.log('station - singleStepTransfer')
    // 没有激活的电话或者激活的电话不是通话状态则返回
    const activeCall = this.calls.get(this.calls.activeCall)
    if (!activeCall || activeCall.state !== CallStateType.CONNECTED) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      uuid: activeCall.callId,
      device: this.stationId,
      deviceTo: dest,
      domain: this.domain,
      uui: this.encodeUui(options.uui),
      businessCode: options.businessCode || '',
      type: '7'
    })
  }

  sendDtmfTone(dtmf: string) {
    console.log('station - sendDtmfTone')
    const activeCall = this.calls.get(this.calls.activeCall)
    if (!activeCall || activeCall.state !== CallStateType.CONNECTED) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    return this.send({
      uuid: activeCall.callId,
      device: this.stationId,
      domain: this.domain,
      dtmf: dtmf,
      type: '15'
    })
  }

  serviceResult(rosterCallId: string, resultReasonCode: string, activityInfoId: string, rosterInfoId: string,
                recallDateTime: string, newPhoneNum1: string, recallMode: string, timeSelectedInterval: string) {
    console.log('station - serviceResult')
    return this.send({
      callId: rosterCallId,
      device: this.stationId,
      domain: this.domain,
      agentId: this.agent.agentId,
      resultCode: resultReasonCode,
      activityInfoId: activityInfoId,
      rosterInfoId: rosterInfoId,
      recallDateTime: recallDateTime,
      newPhoneNum1: newPhoneNum1,
      recallMode: recallMode,
      timeSelectedInterval: timeSelectedInterval,
      type: '30'
    })
  }

  validatePwd(dest1: string, dest2: string, options: any = {}) {
    console.log('station -> client validate password .')
    // 没有保持的或激活的电话则返回
    // 已保持的电话必须处于通话状态
    const activeCall = this.calls.get(this.calls.activeCall)
    if (!activeCall || activeCall.state !== CallStateType.CONNECTED) {
      return Promise.reject(new Error('INVALID_STATE_ERR'))
    }

    // 发送消息
    return this.send({
      uuid: activeCall.callId,
      device: this.stationId,
      domain: this.domain,
      dest1: dest1,
      dest2: dest2,
      uui: this.encodeUui(options.uui),
      type: '20'
    })
  }

  // 按钮状态检查和更新
  private checkButton(e?: any) {
    if (!e) {
      this.initButtonState()
      return
    }

    const activeCall = this.calls.get(this.calls.activeCall)
    const heldCall = this.calls.filter({
      isHeld: true
    }).getLast()
    const ringingCall = this.calls.filter({
      state: CallStateType.RINGING
    }).getLast()

    const buttonInfo = {
      login_btn: false,
      sendDTMF_btn: false,
      makeCall_btn: false,
      hangup_btn: false,
      answer_btn: false,
      consult_btn: false,
      hold_btn: false,
      retrieve_btn: false,
      ssc_btn: false,
      sst_btn: false,
      alternate_btn: false,
      transfer_btn: false,
      reconnect_btn: false,
      conference_btn: false,
      validatePwd_btn: false
    }

    // 可否登录
    if (this.agent.state !== AgentStateType.LOGOUT) {
      buttonInfo.login_btn = false
    } else {
      this.initButtonState()
      return
    }

    // 可否发送DTMF
    buttonInfo.sendDTMF_btn = this.agent.state === AgentStateType.LOGIN

    // 可否外拨
    buttonInfo.makeCall_btn = !(this.calls.callsList.length >= 1)

    // 可否接听
    buttonInfo.answer_btn = Boolean(ringingCall)

    // 可否挂断
    buttonInfo.hangup_btn = this.calls.callsList.length > 0 &&
      this.calls.filter({ state: CallStateType.RINGING }).callsList.length !== this.calls.callsList.length

    // 可否保持
    buttonInfo.hold_btn = this.calls.callsList.length > 0 &&
      this.calls.filter({ state: CallStateType.CONNECTED, isHeld: false }).callsList.length !== 0 &&
      this.calls.callsList.length === 1

    // 可否恢复
    buttonInfo.retrieve_btn = this.calls.callsList.length > 0 &&
      this.calls.filter({ isHeld: true }).callsList.length === this.calls.callsList.length

    // 可否磋商
    buttonInfo.consult_btn = this.calls.callsList.length === 1 &&
      Boolean(activeCall && activeCall.state === CallStateType.CONNECTED)

    // 可否单转和单会议
    if (!activeCall || activeCall.state !== CallStateType.CONNECTED ||
        this.calls.callsList.length >= 2) {
      buttonInfo.sst_btn = false
      buttonInfo.ssc_btn = false
    } else {
      buttonInfo.sst_btn = true
      buttonInfo.ssc_btn = true
    }

    // 多方操作按钮
    if (!activeCall || !heldCall) {
      buttonInfo.conference_btn = false
      buttonInfo.transfer_btn = false
      buttonInfo.alternate_btn = false
      buttonInfo.reconnect_btn = false
    } else {
      if (activeCall.state === CallStateType.CONNECTED && heldCall.state === CallStateType.CONNECTED) {
        if (this.calls.consultationType === 'transfer') {
          buttonInfo.alternate_btn = true
        } else if (this.calls.consultationType === 'conference') {
          buttonInfo.conference_btn = true
        } else {
          buttonInfo.transfer_btn = true
          buttonInfo.conference_btn = true
        }
        buttonInfo.alternate_btn = true
        buttonInfo.reconnect_btn = true
      }
    }

    // 可否验密
    buttonInfo.validatePwd_btn = Boolean(activeCall &&
      activeCall.state === CallStateType.CONNECTED &&
      this.calls.callsList.length !== 2)

    // 通过事件发送按钮状态更新
    this.emitter.emit('ButtonStateChanged', buttonInfo)
  }

  private initButtonState() {
    const buttonInfo = {
      login_btn: true,
      sendDTMF_btn: false,
      makeCall_btn: false,
      hangup_btn: false,
      answer_btn: false,
      consult_btn: false,
      hold_btn: false,
      retrieve_btn: false,
      ssc_btn: false,
      sst_btn: false,
      alternate_btn: false,
      transfer_btn: false,
      reconnect_btn: false,
      conference_btn: false,
      validatePwd_btn: false
    }

    // 通过事件发送初始按钮状态
    this.emitter.emit('ButtonStateChanged', buttonInfo)
  }

  // 辅助方法
  private encodeUui(uui: any): string {
    if (!uui) return ''
    if (typeof uui === 'string') return uui
    try {
      return JSON.stringify(uui)
    } catch (e) {
      return String(uui)
    }
  }

  mute(toggle?: boolean) {
    this.ensure()
    const next = toggle ?? !this.isMuted
    this.isMuted = next
    this.send({ action: 'mute', enabled: next })
  }

  // ==================== CCEvents 事件处理方法 ====================
  private onAgentStateChange(data: CallEventPayload) {
    // 更新按钮状态
    this.checkButton(data)
  }

  private onStationStateChange(data: CallEventPayload) {
    // 更新按钮状态
    this.checkButton(data)
  }

  // 坐席登录事件
  private onAgentLoggedOn(data: CallEventPayload) {
    console.log('srcDevice ',data.srcDevice)
    console.log('srcDevice ',this.srcDevice)
    if (data.srcDevice !== this.srcDevice) return
    console.log('onAgentLoggedOn', data)

    this.agent.lastState = this.agent.state
    this.agent.state = AgentStateType.LOGIN
    this.agent.lastTime = this.agent.loginTime = data.timestamp || Date.now()

    // 更新按钮状态
    this.checkButton(data)
  }

  // 坐席退出事件
  private onAgentLoggedOff(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onAgentLoggedOff', data)

    this.agent.lastState = this.agent.state
    this.agent.state = AgentStateType.LOGOUT
    this.agent.lastTime = this.agent.logoutTime = data.timestamp || Date.now()

    // 关闭连接
    if (this.ws) {
      this.ws.close()
    }
  }

  // 坐席就绪事件
  private onAgentReady(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onAgentReady', data)

    this.agent.lastState = this.agent.state
    this.agent.state = AgentStateType.READY
    this.agent.lastTime = data.timestamp || Date.now()
  }

  // 坐席未就绪事件
  private onAgentNotReady(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onAgentNotReady', data)

    this.agent.lastState = this.agent.state
    this.agent.state = AgentStateType.NOT_READY
    this.agent.lastTime = data.timestamp || Date.now()
  }

  // 坐席后处理事件
  private onAgentWorkingAfterCall(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onAgentWorkingAfterCall', data)

    this.agent.lastState = this.agent.state
    this.agent.state = AgentStateType.WORK_NOT_READY
    this.agent.lastTime = data.timestamp || Date.now()
  }

  // 摘机/外拨发起事件
  private onOriginated(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onOriginated - 外拨', data)

    // 外拨时一定是激活的，外拨状态可以保持
    this.calls.activeCall = data.callId
    this.calls.add({
      callId: data.callId,
      state: CallStateType.DIALING,
      direction: 'Out',
      createTime: data.timestamp || Date.now(),
      isHeld: false,
      callType: 'Outbound'
    })

    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))

    // 更新按钮状态
    this.checkButton(data)
  }

  // 振铃事件
  private onDelivered(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onDelivered', data)

    // 来电时一定不是激活的，振铃状态不可以保持
    if (data.alertingDevice === this.stationId) {
      this.calls.add({
        callId: data.callId,
        createTime: data.timestamp || Date.now(),
        caller: data.callingDevice,
        callee: data.calledDevice,
        state: CallStateType.RINGING,
        direction: 'In',
        isHeld: false,
        queue: data.skill || '',
        alertingDevice: data.alertingDevice,
        callType: data.skill ? 'Inbound' : 'Internal'
      })
      console.log('RINGING - 振铃')
    } else {
      // 外拨到达
      this.calls.add({
        callId: data.callId,
        state: CallStateType.DIALING,
        direction: 'Out',
        caller: data.callingDevice,
        callee: data.calledDevice,
        alertingDevice: data.alertingDevice
      })

      if (this.calls.consultationTo === data.alertingDevice) {
        this.calls.consultationTo = data.callId
      } else if (this.calls.callsList.length === 2 && this.calls.consultationFrom) {
        this.calls.consultationTo = data.callId
      }
      console.log('Delivered - 到达')
    }

    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))

    // 更新按钮状态
    this.checkButton(data)
  }

  // 接通事件
  private onEstablished(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onEstablished - 接通', data)

    const lastStateCall = this.calls.get(data.callId)
    // 之前存在，并且不是保持的
    if (!lastStateCall || !lastStateCall.isHeld) {
      this.calls.activeCall = data.callId
    }

    this.calls.add({
      callId: data.callId,
      state: CallStateType.CONNECTED,
      answerTime: data.timestamp || Date.now()
    })

    console.log('Established - 接通')
    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))

    // 更新按钮状态
    this.checkButton(data)
  }

  // 挂断事件
  private onConnectionCleared(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onConnectionCleared - 挂断', data)

    // 自己挂断
    if (this.stationId === data.releasingDevice) {
      // 清除磋商标记
      if (data.callId === this.calls.consultationFrom || data.callId === this.calls.consultationTo) {
        this.calls.clearConsultation()
      }

      this.calls.remove(data.callId)
      if (data.callId === this.calls.activeCall) {
        this.calls.activeCall = ''
      }

      const activeCall = this.calls.filter({ isHeld: false }).getLast()
      if (activeCall && !this.calls.get(this.calls.activeCall)) {
        this.calls.activeCall = activeCall.callId
      }

      // 触发通话结束事件
      this.emitter.emit('CallEnd', {})
    } else if ('Fail' === data.connectionState) {
      // 连接失败，暂不处理
    }

    console.log('ConnectionCleared - 挂断')
    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))

    // 更新按钮状态
    this.checkButton(data)
  }

  // 保持事件
  private onHeld(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onHeld - 保持', data)

    // 已激活的callId并且是保持方
    if (data.holdingDevice === this.stationId) {
      this.calls.add({
        callId: data.callId,
        isHeld: true
      })
    }

    if (data.callId === this.calls.activeCall) {
      this.calls.activeCall = ''
    }

    console.log('Held - 保持')
    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))
  }

  // 恢复事件
  private onRetrieved(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onRetrieved - 恢复', data)

    // 已保持的callId并且是恢复方
    if (data.retrievingDevice === this.stationId) {
      this.calls.replace(data.callId, {
        callId: data.callId,
        isHeld: false
      })

      if (this.calls.callsList.length) {
        this.calls.activeCall = data.callId
      }

      console.log('Retrieved - 恢复')
      console.log('activeCall:', this.calls.activeCall)
      console.log('calls:', JSON.stringify(this.calls.callsList))
    }
  }

  // 会议事件
  private onConferenced(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onConferenced - 会议', data)

    const clearCall = this.calls.consultationTo === data.newCall ? this.calls.consultationFrom : this.calls.consultationTo
    this.calls.clearConsultation()
    this.calls.remove(clearCall)

    const lastCall = this.calls.getLast()
    if (lastCall) {
      this.calls.replace(lastCall.callId, {
        callId: data.newCall,
        isHeld: false,
        state: CallStateType.CONNECTED
      })
    }
    this.calls.activeCall = data.newCall

    console.log('Conferenced - 会议')
    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))
  }

  // 转移事件
  private onTransfered(data: CallEventPayload) {
    if (data.srcDevice !== this.srcDevice) return
    console.log('onTransfered - 转移', data)

    if (data.primaryOldCall === this.calls.consultationFrom || data.secondaryOldCall === this.calls.consultationFrom
        || data.primaryOldCall === this.calls.consultationTo || data.secondaryOldCall === this.calls.consultationTo) {
      this.calls.clearConsultation()
    }

    // 自己是转移方
    if (data.transferringDevice === this.stationId && data.transferringDevice !== data.transferredToDevice) {
      this.calls.activeCall = ''
      this.calls.remove(data.primaryOldCall)
      this.calls.remove(data.secondaryOldCall)
    } else {
      let existCall: string
      if (this.calls.get(data.primaryOldCall)) {
        existCall = data.primaryOldCall
      } else {
        existCall = data.secondaryOldCall
      }

      // 更新旧的callId，旧的电话为激活则更新激活的电话
      this.calls.replace(existCall, {
        callId: data.newCall,
      })
      if (this.calls.activeCall) {
        this.calls.activeCall = data.newCall
      }
    }

    console.log('Transferred - 转移')
    console.log('activeCall:', this.calls.activeCall)
    console.log('calls:', JSON.stringify(this.calls.callsList))
  }

  // 排队事件
  private onQueueCallChange(data: CallEventPayload) {
    console.log('onQueueCallChange', data)
    this.queueMap.set(data.skill, data.count)
    if (!this.agent.queues.includes(data.skill)) {
      this.agent.queues.push(data.skill)
    }
  }

  // 密码验证事件
  private onValidatePwd(data: CallEventPayload) {
    console.log('onValidatePwd', data)
    this.validate = data.validatePwd
    console.log('validate password result:', data.validatePwd)
  }

  private ensure() {
    if (!this.connected) this.connect({})
  }

  private send(payload: Record<string, unknown>): Promise<any> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('CallService: WebSocket not connected')
      return Promise.reject(new Error('WebSocket not connected'))
    }
    this.ws.send(JSON.stringify(payload))
    return Promise.resolve()
  }

  private mapEventToStatus(evtName: string) {
    switch (evtName) {
      case 'OriginatedEvt':
        this.setStatus('DIALING')
        break
      case 'DeliveredEvt':
        this.setStatus('RINGING')
        break
      case 'EstablishedEvt':
        this.setStatus('CONNECTED')
        break
      case 'ConnectionClearedEvt':
        this.setStatus('ENDED')
        break
      case 'HeldEvt':
        this.setStatus('HELD')
        break
      default:
        break
    }
  }

  private setStatus(next: CallStatus) {
    if (this.status === next) return
    this.status = next
    this.emitter.emit(CallEvents.StatusChanged, next)
  }
}

export const callService = new CallService()
export default callService 