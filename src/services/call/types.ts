export type CallStatus = 'IDLE' | 'DIALING' | 'RINGING' | 'CONNECTED' | 'HELD' | 'ENDED'

export interface CallEventPayload {
  evtName?: string
  [key: string]: any
}

export const CallEvents = {
  WsOpen: 'CallWsOpen',
  WsClose: 'CallWsClose',
  WsError: 'CallWsError',
  Raw: 'CallRawEvent',
  StatusChanged: 'CallStatusChanged',
  ButtonStateChanged: 'ButtonStateChanged'
} as const

export type CallEventName = typeof CallEvents[keyof typeof CallEvents]

// Selected softphone backend events to listen to in UI
export const CCEvents = {
  AgentLoggedOnEvt: 'AgentLoggedOnEvt',
  AgentStateChangeEvt: 'AgentStateChangeEvt',
  StationStateChangeEvt: 'StationStateChangeEvt',
  AgentReadyEvt: 'AgentReadyEvt',
  AgentNotReadyEvt: 'AgentNotReadyEvt',
  AgentWorkingAfterCallEvt: 'AgentWorkingAfterCallEvt',
  AgentLoggedOffEvt: 'AgentLoggedOffEvt',
  QueueCallChangeEvt: 'QueueCallChangeEvt',
  OriginatedEvt: 'OriginatedEvt',
  DeliveredEvt: 'DeliveredEvt',
  EstablishedEvt: 'EstablishedEvt',
  ConnectionClearedEvt: 'ConnectionClearedEvt',
  ValidatePwdEvt: 'ValidatePwdEvt',
  HeldEvt: 'HeldEvt',
  RetrievedEvt: 'RetrievedEvt',
  ConferencedEvt: 'ConferencedEvt',
  TransferedEvt: 'TransferedEvt',
  Error: 'Error'
} as const

// Call 状态枚举
export enum CallStateType {
  DEAD = 'DEAD',
  DIALING = 'DIALING',
  RINGING = 'RINGING',
  CONNECTED = 'CONNECTED',
  HELD = 'HELD'
}

// Agent 状态枚举
export enum AgentStateType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  READY = 'READY',
  NOT_READY = 'NOT_READY',
  WORK_NOT_READY = 'WORK_NOT_READY'
}

// Call 类
export class Call {
  callId: string = ''
  state: CallStateType = CallStateType.DEAD
  createTime: number = 0
  answerTime: number = 0
  caller: string = ''
  callee: string = ''
  callType: string = ''
  alertingDevice: string = ''
  direction: string = ''
  isHeld: boolean = false
  queue: string = ''
}

// Calls 管理类
export class Calls {
  activeCall: string = ''
  consultationType: string = ''
  consultationFrom: string = ''
  consultationTo: string = ''
  callsList: Call[] = []

  get(callId: string): Call | undefined {
    return this.callsList.find(item => item.callId === callId)
  }

  add(call: Partial<Call>) {
    if (call.callId) {
      let origCall = this.get(call.callId)
      if (!origCall) {
        origCall = new Call()
        this.callsList.push(origCall)
      }
      // 更新属性
      Object.keys(origCall).forEach(key => {
        if (call[key] !== undefined) {
          origCall[key] = call[key]
        }
      })
    }
  }

  remove(callId: string) {
    for (let i = 0, len = this.callsList.length; i < len; i++) {
      if (this.callsList[i].callId === callId) {
        this.callsList.splice(i, 1)
        return
      }
    }
  }

  removeIndex(index: number) {
    this.callsList.splice(index, 1)
  }

  clear() {
    this.callsList.length = 0
  }

  clearConsultation() {
    this.consultationFrom = ''
    this.consultationTo = ''
    this.consultationType = ''
  }

  getIndex(index: number): Call | null {
    return this.callsList[index] || null
  }

  getLast(): Call | null {
    return this.callsList[this.callsList.length - 1] || null
  }

  indexOf(callId: string): number {
    for (let i = 0, len = this.callsList.length; i < len; i++) {
      if (this.callsList[i].callId === callId) return i
    }
    return -1
  }

  size(): number {
    return this.callsList.length
  }

  replace(callId: string, call: Partial<Call>) {
    const origCall = this.get(callId)
    if (origCall) {
      Object.keys(origCall).forEach(key => {
        if (call[key] !== undefined) {
          origCall[key] = call[key]
        }
      })
    }
  }

  filter(condition: Partial<Call>): Calls {
    const calls = new Calls()
    // 命名循环
    callsLoop: for (let i = 0, len = this.callsList.length; i < len; i++) {
      for (const key in condition) {
        if (this.callsList[i][key] === undefined || this.callsList[i][key] !== condition[key]) {
          continue callsLoop
        }
      }
      calls.callsList.push(this.callsList[i])
    }
    return calls
  }
}

// Agent 类
export class Agent {
  agentId: string = ''
  state: AgentStateType = AgentStateType.LOGOUT
  lastState: AgentStateType = AgentStateType.LOGOUT
  lastTime: number = Date.now()
  loginTime?: number
  logoutTime?: number
  queues: string[] = []
}