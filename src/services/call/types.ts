export type CallStatus = 'IDLE' | 'DIALING' | 'RINGING' | 'CONNECTED' | 'HELD' | 'ENDED'

export interface CallEventPayload {
  evtName?: string
  [key: string]: any
}

export const CallEvents = {
  WsOpen: 'CallWsOpen',
  WsClose: 'CallWsClose',
  WsError: 'CallWsError',
  Raw: 'CallRawEvent',
  StatusChanged: 'CallStatusChanged',
  ButtonStateChanged: 'ButtonStateChanged'
} as const

export type CallEventName = typeof CallEvents[keyof typeof CallEvents]

// Selected softphone backend events to listen to in UI
export const CCEvents = {
  AgentLoggedOnEvt: 'AgentLoggedOnEvt',
  AgentStateChangeEvt: 'AgentStateChangeEvt',
  StationStateChangeEvt: 'StationStateChangeEvt',
  AgentReadyEvt: 'AgentReadyEvt',
  AgentNotReadyEvt: 'AgentNotReadyEvt',
  AgentWorkingAfterCallEvt: 'AgentWorkingAfterCallEvt',
  AgentLoggedOffEvt: 'AgentLoggedOffEvt',
  QueueCallChangeEvt: 'QueueCallChangeEvt',
  OriginatedEvt: 'OriginatedEvt',
  DeliveredEvt: 'DeliveredEvt',
  EstablishedEvt: 'EstablishedEvt',
  ConnectionClearedEvt: 'ConnectionClearedEvt',
  ValidatePwdEvt: 'ValidatePwdEvt',
  HeldEvt: 'HeldEvt',
  RetrievedEvt: 'RetrievedEvt',
  ConferencedEvt: 'ConferencedEvt',
  TransferedEvt: 'TransferedEvt',
  Error: 'Error'
} as const 